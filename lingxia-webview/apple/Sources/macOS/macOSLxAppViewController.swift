#if os(macOS)
import Foundation
import WebKit
import os.log
import Cocoa
import CLingXiaFFI

private let lxAppViewControllerLog = OSLog(subsystem: "LingXia", category: "LxAppView")

@MainActor
public class macOSLxAppViewController: NSViewController, WKNavigationDelegate {
    nonisolated(unsafe) private static let log = lxAppViewControllerLog

    // MARK: - Constants
    private static let TAB_BAR_HEIGHT: CGFloat = 40
    internal static let DEFAULT_NAV_BAR_HEIGHT: CGFloat = 32 // This constant is no longer used for layout, but kept for reference if needed elsewhere

    // MARK: - Properties
    internal var appId: String
    private var initialPath: String
    private var webViewContainer: NSView!
    private var tabBarView: NSView?
    private var currentWebView: WKWebView?
    // Removed: private var navigationBar: LingXiaNavigationBar!

    nonisolated(unsafe) private var closeAppObserver: NSObjectProtocol?
    nonisolated(unsafe) private var switchPageObserver: NSObjectProtocol?

    public init(appId: String, path: String) {
        self.appId = appId
        self.initialPath = path
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    deinit {
        os_log("Deinitializing LxAppViewController for appId: %@", log: Self.log, type: .info, appId)
        if let observer = closeAppObserver {
            NotificationCenter.default.removeObserver(observer)
        }
        if let observer = switchPageObserver {
            NotificationCenter.default.removeObserver(observer)
        }
    }

    // MARK: - Lifecycle
    public override func loadView() {
        view = NSView()
        view.wantsLayer = true
        view.layer?.backgroundColor = NSColor.windowBackgroundColor.cgColor
    }

    public override func viewDidLoad() {
        super.viewDidLoad()
        os_log("ViewDidLoad started for appId: %@", log: Self.log, type: .info, appId)
        
        // Set view background color for visibility
        view.wantsLayer = true
        view.layer?.backgroundColor = NSColor.windowBackgroundColor.cgColor
        
        // Ensure view size matches window content view
        if let window = view.window, let contentView = window.contentView {
            view.frame = contentView.bounds
        }
        
        // Setup UI components
        setupLayout()
        setupNotificationObservers()
        loadWebViewContent()
        
        // Force layout update
        view.needsLayout = true
        view.layoutSubtreeIfNeeded()
        
        os_log("ViewDidLoad completed", log: Self.log, type: .info)
    }

    // MARK: - UI Setup
    private func setupLayout() {
        os_log("Setting up UI layout for appId: %@", log: Self.log, type: .info, appId)
        
        // Set main view background
        view.wantsLayer = true
        view.layer?.backgroundColor = NSColor.windowBackgroundColor.cgColor
        
        // Ensure view size is correct
        if let window = view.window, let contentView = window.contentView {
            view.frame = contentView.bounds
        }
        
        // Create TabBar first
        setupTabBar()
        
        // Create WebView container
        setupWebViewContainer()
        
        // Add TabBar to view hierarchy and set constraints based on position
        if let tabBar = tabBarView, let tabBarConfig = getTabBarConfig(appId), let config = TabBarConfig.fromJson(tabBarConfig.toString()) {
            view.addSubview(tabBar)
            
            // Get TabBar height from constants or config
            let tabBarHeight: CGFloat = Self.TAB_BAR_HEIGHT // Default height
            
            // Set TabBar position based on config
            if config.position == "top" {
                // TabBar at top
                NSLayoutConstraint.activate([
                    tabBar.topAnchor.constraint(equalTo: view.topAnchor),
                    tabBar.leadingAnchor.constraint(equalTo: view.leadingAnchor),
                    tabBar.trailingAnchor.constraint(equalTo: view.trailingAnchor),
                    tabBar.heightAnchor.constraint(equalToConstant: tabBarHeight),
                    
                    webViewContainer.topAnchor.constraint(equalTo: tabBar.bottomAnchor),
                    webViewContainer.leadingAnchor.constraint(equalTo: view.leadingAnchor),
                    webViewContainer.trailingAnchor.constraint(equalTo: view.trailingAnchor),
                    webViewContainer.bottomAnchor.constraint(equalTo: view.bottomAnchor)
                ])
            } else {
                // TabBar at bottom (default)
                NSLayoutConstraint.activate([
                    tabBar.bottomAnchor.constraint(equalTo: view.bottomAnchor),
                    tabBar.leadingAnchor.constraint(equalTo: view.leadingAnchor),
                    tabBar.trailingAnchor.constraint(equalTo: view.trailingAnchor),
                    tabBar.heightAnchor.constraint(equalToConstant: tabBarHeight),
                    
                    webViewContainer.topAnchor.constraint(equalTo: view.topAnchor),
                    webViewContainer.leadingAnchor.constraint(equalTo: view.leadingAnchor),
                    webViewContainer.trailingAnchor.constraint(equalTo: view.trailingAnchor),
                    webViewContainer.bottomAnchor.constraint(equalTo: tabBar.topAnchor)
                ])
            }
        } else {
            // No TabBar, WebView container takes full height
            NSLayoutConstraint.activate([
                webViewContainer.topAnchor.constraint(equalTo: view.topAnchor),
                webViewContainer.leadingAnchor.constraint(equalTo: view.leadingAnchor),
                webViewContainer.trailingAnchor.constraint(equalTo: view.trailingAnchor),
                webViewContainer.bottomAnchor.constraint(equalTo: view.bottomAnchor)
            ])
        }
        
        // Force layout update
        view.needsLayout = true
        view.layoutSubtreeIfNeeded()
    }
    
    private func addDebugLabel() {
        let label = NSTextField(labelWithString: "LingXia Debug - \(appId)")
        label.translatesAutoresizingMaskIntoConstraints = false
        label.textColor = NSColor.white
        label.backgroundColor = NSColor.black
        label.alignment = .center
        label.font = NSFont.boldSystemFont(ofSize: 16)
        
        view.addSubview(label)
        
        NSLayoutConstraint.activate([
            label.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            label.topAnchor.constraint(equalTo: view.topAnchor, constant: 40),
            label.widthAnchor.constraint(equalToConstant: 200),
            label.heightAnchor.constraint(equalToConstant: 30)
        ])
    }

    // Removed: private func setupNavigationBar() { ... }

    private func setupWebViewContainer() {
        webViewContainer = NSView()
        webViewContainer.wantsLayer = true
        webViewContainer.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(webViewContainer)
    }

    private func setupTabBar() {
        guard let tabBarConfigRust = getTabBarConfig(appId) else {
            os_log("No TabBar config found for appId: %@", log: Self.log, type: .info, appId)
            return
        }

        let tabBarConfigJson = tabBarConfigRust.toString()
        os_log("TabBar config JSON: %@", log: Self.log, type: .debug, tabBarConfigJson)
        
        guard let tabBarConfig = TabBarConfig.fromJson(tabBarConfigJson) else {
            os_log("Failed to parse TabBar config for appId: %@", log: Self.log, type: .error, appId)
            return
        }
        
        // Check if TabBar is hidden in config
        if tabBarConfig.hidden {
            os_log("TabBar is hidden in config", log: Self.log, type: .info)
            return
        }

        let tabBar = NSView()
        tabBar.wantsLayer = true
        
        // Set TabBar background color based on config
        if let backgroundColor = tabBarConfig.backgroundColor {
            tabBar.layer?.backgroundColor = backgroundColor.cgColor
        } else {
            tabBar.layer?.backgroundColor = TabBarConfig.DEFAULT_BACKGROUND_COLOR.cgColor
        }
        
        // Set transparency if specified in config
        if tabBarConfig.transparent {
            tabBar.layer?.backgroundColor = NSColor.clear.cgColor
        }
        
        // Add top separator line
        let separatorView = NSView()
        separatorView.wantsLayer = true
        separatorView.layer?.backgroundColor = NSColor.separatorColor.cgColor
        separatorView.translatesAutoresizingMaskIntoConstraints = false
        tabBar.addSubview(separatorView)
        
        tabBar.translatesAutoresizingMaskIntoConstraints = false

        let stackView = NSStackView()
        stackView.orientation = .horizontal
        stackView.distribution = .fillEqually
        stackView.alignment = .centerY
        stackView.spacing = 0 // No spacing between items
        stackView.translatesAutoresizingMaskIntoConstraints = false
        tabBar.addSubview(stackView)

        NSLayoutConstraint.activate([
            // Separator constraints - position based on TabBar position
            separatorView.leadingAnchor.constraint(equalTo: tabBar.leadingAnchor),
            separatorView.trailingAnchor.constraint(equalTo: tabBar.trailingAnchor),
            separatorView.topAnchor.constraint(equalTo: tabBar.topAnchor),
            separatorView.heightAnchor.constraint(equalToConstant: 1),
            
            // Stack view constraints
            stackView.leadingAnchor.constraint(equalTo: tabBar.leadingAnchor),
            stackView.trailingAnchor.constraint(equalTo: tabBar.trailingAnchor),
            stackView.topAnchor.constraint(equalTo: tabBar.topAnchor),
            stackView.bottomAnchor.constraint(equalTo: tabBar.bottomAnchor)
        ])

        for (index, item) in tabBarConfig.items.enumerated() {
            // Create container view
            let containerView = NSView()
            containerView.wantsLayer = true
            containerView.translatesAutoresizingMaskIntoConstraints = false
            
            // Create button
            let button = NSButton(title: "", target: self, action: #selector(tabButtonClicked(_:)))
            button.tag = index
            button.bezelStyle = .regularSquare
            button.isBordered = false
            button.wantsLayer = true
            button.layer?.backgroundColor = NSColor.clear.cgColor
            button.translatesAutoresizingMaskIntoConstraints = false
            
            // Create icon view
            let iconView = NSImageView()
            iconView.translatesAutoresizingMaskIntoConstraints = false
            if let iconPath = item.iconPath {
                setButtonIconForView(imageView: iconView, iconPath: iconPath)
            }
            
            // Add views to container
            containerView.addSubview(iconView)
            containerView.addSubview(button) // Button on top to receive clicks
            
            // Set constraints
            NSLayoutConstraint.activate([
                // Icon constraints - center in container
                iconView.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
                iconView.centerYAnchor.constraint(equalTo: containerView.centerYAnchor),
                iconView.widthAnchor.constraint(equalToConstant: 24),
                iconView.heightAnchor.constraint(equalToConstant: 24),
                
                // Button covers entire container
                button.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
                button.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
                button.topAnchor.constraint(equalTo: containerView.topAnchor),
                button.bottomAnchor.constraint(equalTo: containerView.bottomAnchor)
            ])
            
            stackView.addArrangedSubview(containerView)
        }

        self.tabBarView = tabBar
        os_log("✅ TabBar setup completed with %d items", log: Self.log, type: .info, tabBarConfig.items.count)
    }

    private func loadWebViewContent() {
        os_log("Loading WebView content for appId: %@ path: %@", log: Self.log, type: .info, appId, initialPath)
        
        // Try to find WebView - WebView is created by Rust
        if let webView = SharedWebViewManager.findWebView(appId: appId, path: initialPath) {
            attachWebViewToContainer(webView)
        } else {
            // If WebView not found, wait and retry
            tryAttachWebView(retryCount: 0)
        }
        
        // Force layout update
        webViewContainer.needsLayout = true
        webViewContainer.layoutSubtreeIfNeeded()
    }
    
    private func tryAttachWebView(retryCount: Int) {
        if let webView = SharedWebViewManager.findWebView(appId: appId, path: initialPath) {
            attachWebViewToContainer(webView)
            os_log("WebView found and attached on attempt %d", log: Self.log, type: .info, retryCount)
        } else if retryCount < 5 {  // Try up to 5 times
            os_log("WebView not found, will retry... (attempt %d)", log: Self.log, type: .info, retryCount + 1)
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
                guard let self = self else { return }
                self.tryAttachWebView(retryCount: retryCount + 1)
            }
        } else {
            os_log("Failed to find WebView after %d attempts", log: Self.log, type: .error, retryCount)
        }
    }
    
    private func attachWebViewToContainer(_ webView: WKWebView) {
        currentWebView?.removeFromSuperview()
        currentWebView = webView
        
        webView.translatesAutoresizingMaskIntoConstraints = false
        webViewContainer.addSubview(webView)
        
        NSLayoutConstraint.activate([
            webView.topAnchor.constraint(equalTo: webViewContainer.topAnchor),
            webView.leadingAnchor.constraint(equalTo: webViewContainer.leadingAnchor),
            webView.trailingAnchor.constraint(equalTo: webViewContainer.trailingAnchor),
            webView.bottomAnchor.constraint(equalTo: webViewContainer.bottomAnchor)
        ])
        
        // Force layout update - use macOS compatible method
        #if os(macOS)
        webView.needsLayout = true
        webViewContainer.needsLayout = true
        webViewContainer.layoutSubtreeIfNeeded()
        #else
        // iOS version
        webView.setNeedsLayout()
        webView.layoutIfNeeded()
        webViewContainer.setNeedsLayout()
        webViewContainer.layoutIfNeeded()
        #endif
        
        // Ensure WebView is visible
        webView.isHidden = false
        #if os(iOS)
        webView.alpha = 1.0
        #endif
        
        webView.navigationDelegate = self
        let _ = onPageShow(appId, initialPath)
        os_log("WebView attached to container", log: Self.log, type: .info)
    }

    private func setupNotificationObservers() {
        closeAppObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name(ACTION_CLOSE_LXAPP), object: nil, queue: .main
        ) { [weak self] notification in
            let appId = notification.userInfo?["appId"] as? String
            Task { @MainActor in
                guard let self = self, let targetAppId = appId, targetAppId == self.appId else { return }
                os_log("Received close request for appId: %@", log: Self.log, type: .info, self.appId)
                self.view.window?.close()
            }
        }

        switchPageObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name(ACTION_SWITCH_PAGE), object: nil, queue: .main
        ) { [weak self] notification in
            let appId = notification.userInfo?["appId"] as? String
            let path = notification.userInfo?["path"] as? String
            Task { @MainActor in
                guard let self = self, let targetAppId = appId, let targetPath = path, targetAppId == self.appId else { return }
                os_log("Received switch page notification - appId: %@ path: %@", log: Self.log, type: .info, self.appId, targetPath)
                self.switchPage(targetPath: targetPath)
            }
        }
        os_log("Notification observers set up for appId: %@", log: Self.log, type: .info, appId)
    }

    // MARK: - Page Switching
    public func switchPage(targetPath: String) {
        self.initialPath = targetPath
        os_log("Switching to page: %@", log: Self.log, type: .info, targetPath)

        let _ = onPageShow(appId, targetPath)
        let _ = findWebView(appId, targetPath)
        
        if let webView = SharedWebViewManager.findWebView(appId: appId, path: targetPath) {
            attachWebViewToContainer(webView)
            os_log("✅ Switched to page: %@", log: Self.log, type: .info, targetPath)
        } else {
            os_log("❌ WebView not found for page: %@", log: Self.log, type: .error, targetPath)
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                if let webView = SharedWebViewManager.findWebView(appId: self.appId, path: targetPath) {
                    self.attachWebViewToContainer(webView)
                    os_log("✅ WebView found after retry and attached", log: Self.log, type: .info)
                }
            }
        }
        SharedLxApp.setLastActivePath(targetPath, for: appId)
    }

    // MARK: - TabBar Actions
    @objc private func tabButtonClicked(_ sender: NSButton) {
        let index = sender.tag
        os_log("Tab button clicked: index=%d", log: Self.log, type: .info, index)

        guard let tabBarConfigRust = getTabBarConfig(appId),
              let tabBarConfig = TabBarConfig.fromJson(tabBarConfigRust.toString()),
              index < tabBarConfig.items.count else {
            return
        }
        let item = tabBarConfig.items[index]
        switchPage(targetPath: item.pagePath)
    }

    // MARK: - Helpers
    private func setButtonIcon(button: NSButton, iconPath: String) {
        var image: NSImage?
        print("DEBUG: Setting icon for path: \(iconPath)")
        
        if iconPath.hasPrefix("SF:") {
            let symbolName = String(iconPath.dropFirst(3))
            if #available(macOS 11.0, *) {
                let config = NSImage.SymbolConfiguration(pointSize: 28, weight: .medium) // 更大的图标
                image = NSImage(systemSymbolName: symbolName, accessibilityDescription: nil)?.withSymbolConfiguration(config)
                image?.isTemplate = true
                print("DEBUG: Using SF Symbol: \(symbolName)")
            }
        } else if iconPath.hasPrefix("/") {
            image = NSImage(contentsOfFile: iconPath)
            print("DEBUG: Loading icon from absolute path: \(iconPath)")
        } else {
            // 先尝试作为命名图像加载
            image = NSImage(named: iconPath)
            
            // 如果失败，尝试从资源目录加载
            if image == nil {
                let resourcesPath = getResourcesPath()
                let fullPath = "\(resourcesPath)/homelxapp/\(iconPath)"
                image = NSImage(contentsOfFile: fullPath)
                print("DEBUG: Loading icon from resources path: \(fullPath)")
            }
        }

        if let img = image {
            print("DEBUG: Icon loaded successfully, size: \(img.size)")
            let iconSize = NSSize(width: 32, height: 32) // 更大的图标尺寸
            button.image = resizeImage(img, to: iconSize)
        } else {
            print("DEBUG: Failed to load icon for path: \(iconPath)")
        }
    }

    private func setButtonIconForView(imageView: NSImageView, iconPath: String) {
        var image: NSImage?
        
        if iconPath.hasPrefix("SF:") {
            let symbolName = String(iconPath.dropFirst(3))
            if #available(macOS 11.0, *) {
                let config = NSImage.SymbolConfiguration(pointSize: 24, weight: .medium)
                image = NSImage(systemSymbolName: symbolName, accessibilityDescription: nil)?.withSymbolConfiguration(config)
                image?.isTemplate = true
            }
        } else if iconPath.hasPrefix("/") {
            image = NSImage(contentsOfFile: iconPath)
        } else {
            // Try loading as named image first
            image = NSImage(named: iconPath)
            
            // If failed, try loading from resources directory
            if image == nil {
                let resourcesPath = getResourcesPath()
                let fullPath = "\(resourcesPath)/homelxapp/\(iconPath)"
                image = NSImage(contentsOfFile: fullPath)
            }
        }

        if let img = image {
            let iconSize = NSSize(width: 24, height: 24)
            imageView.image = resizeImage(img, to: iconSize)
        }
    }

    private func getResourcesPath() -> String {
        let executablePath = Bundle.main.executablePath ?? ""
        let executableDir = (executablePath as NSString).deletingLastPathComponent
        return "\(executableDir)/Resources"
    }

    private func resizeImage(_ image: NSImage, to size: NSSize) -> NSImage {
        let resizedImage = NSImage(size: size)
        resizedImage.lockFocus()
        
        // Draw image to fit size
        let drawRect = NSRect(origin: .zero, size: size)
        image.draw(in: drawRect)
        
        resizedImage.unlockFocus()
        resizedImage.isTemplate = image.isTemplate
        
        return resizedImage
    }

    // MARK: - WKNavigationDelegate
    public func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        os_log("✅ WebView finished loading successfully", log: Self.log, type: .info)
    }

    public func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        os_log("❌ WebView failed to load: %@", log: Self.log, type: .error, error.localizedDescription)
    }

    public func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        os_log("❌ WebView provisional navigation failed: %@", log: Self.log, type: .error, error.localizedDescription)
    }
}

// MARK: - LingXiaNavigationBar Class (No longer used for main navigation bar in macOS)
public class LingXiaNavigationBar: NSView {
    private static let TITLE_FONT_SIZE: CGFloat = 17
    private static let TITLE_FONT_WEIGHT: NSFont.Weight = .medium
    private static let BACKGROUND_COLOR = NSColor(red: 0.93, green: 0.93, blue: 0.93, alpha: 1.0)
    private static let BORDER_COLOR = NSColor(red: 0.87, green: 0.87, blue: 0.87, alpha: 1.0)

    private var titleLabel: NSTextField!

    public override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }

    private func setupView() {
        wantsLayer = true
        layer?.backgroundColor = Self.BACKGROUND_COLOR.cgColor

        let bottomBorder = NSView()
        bottomBorder.wantsLayer = true
        bottomBorder.layer?.backgroundColor = Self.BORDER_COLOR.cgColor
        bottomBorder.translatesAutoresizingMaskIntoConstraints = false
        addSubview(bottomBorder)

        titleLabel = NSTextField(labelWithString: "")
        titleLabel.font = NSFont.systemFont(ofSize: Self.TITLE_FONT_SIZE, weight: Self.TITLE_FONT_WEIGHT)
        titleLabel.textColor = NSColor.labelColor
        titleLabel.alignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        addSubview(titleLabel)

        NSLayoutConstraint.activate([
            bottomBorder.leadingAnchor.constraint(equalTo: leadingAnchor),
            bottomBorder.trailingAnchor.constraint(equalTo: trailingAnchor),
            bottomBorder.bottomAnchor.constraint(equalTo: bottomAnchor),
            bottomBorder.heightAnchor.constraint(equalToConstant: 0.5),
            
            titleLabel.centerXAnchor.constraint(equalTo: centerXAnchor),
            titleLabel.centerYAnchor.constraint(equalTo: centerYAnchor)
        ])
    }

    public func setTitle(_ title: String) {
        titleLabel.stringValue = title
    }
}
#endif