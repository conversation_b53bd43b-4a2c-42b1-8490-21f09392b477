# Bundle Name 硬编码问题优化方案

## 问题描述

在 `lingxia-webview/src/apple/resources.rs` 的 `get_resource_bundle` 函数中，存在硬编码的 bundle 名称：

```rust
let bundle_names = [
    "LingXiaDemo_LingXiaDemo",  // Current project bundle
    "miniapp_miniapp",          // Original bundle name
    "lingxia_lingxia",          // Alternative naming
];
```

这种硬编码方式导致不同项目需要修改代码才能正确加载资源，缺乏灵活性。

## 优化方案

### 1. 传入 Bundle Name（已实现）

**优势：**
- 最灵活的方案
- 支持任意 bundle 名称
- 向后兼容

**实现方式：**
- 添加 `set_bundle_name(bundle_name: Option<String>)` FFI 函数
- 使用 `OnceLock` 存储全局 bundle 名称配置
- 在 Swift 初始化代码中自动检测并设置 bundle 名称

**代码流程：**
1. Swift 代码在初始化时调用 `detectBundleName()` 自动检测 bundle 名称
2. 通过 FFI 调用 `setBundleName()` 设置到 Rust 层
3. Rust 的 `get_resource_bundle()` 优先使用配置的 bundle 名称

### 2. Native API 自动检测（已实现）

**实现方式：**
- 使用 `Bundle.main.bundleIdentifier` 获取应用标识符
- 提取最后一个组件，按 SPM 规范生成 bundle 名称格式：`{name}_{name}`
- 在 Rust 层也实现相同的检测逻辑作为备用方案

**检测逻辑：**
```swift
private static func detectBundleName() -> String? {
    guard let bundleIdentifier = Bundle.main.bundleIdentifier else { return nil }
    
    if let lastComponent = bundleIdentifier.split(separator: ".").last {
        let bundleName = "\(lastComponent)_\(lastComponent)"
        return bundleName
    }
    
    return nil
}
```

### 3. 多层级回退机制（已实现）

**回退顺序：**
1. **配置的 bundle 名称** - 通过 `set_bundle_name()` 设置的名称
2. **自动检测的 bundle 名称** - 基于 bundle identifier 检测
3. **硬编码的回退名称** - 保留原有的硬编码名称作为最后回退
4. **主 bundle** - 最终回退到主 bundle

## 使用方式

### 自动模式（推荐）
无需任何配置，系统会自动检测并设置正确的 bundle 名称。

### 手动配置模式
如果自动检测失败，可以在 Swift 初始化代码中手动设置：

```swift
// 在 miniappInit 之前调用
setBundleName("YourProject_YourProject")
```

## 优势

1. **向后兼容** - 保留原有硬编码名称作为回退
2. **自动化** - 大多数情况下无需手动配置
3. **灵活性** - 支持任意 bundle 名称
4. **可靠性** - 多层级回退确保资源加载成功
5. **可维护性** - 减少硬编码，便于维护

## 适用平台

- ✅ iOS
- ✅ macOS
- ❌ HarmonyOS（不适用，HarmonyOS 使用不同的资源管理机制）
- ❌ Android（不适用，Android 使用不同的资源管理机制）

## 测试建议

1. 测试不同 bundle identifier 的项目
2. 测试 SPM 资源包的正确加载
3. 测试回退机制的有效性
4. 验证日志输出的正确性
