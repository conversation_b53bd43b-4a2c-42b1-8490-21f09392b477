lingxia-ui + ui native: master/entry crate, liblingxia.so for android harmony etc
lingxia-webview: crate. export to manifest for apple
lingxia-miniapp: core crate
lingxia-user:  preserved for user and local ---> pack to liblingxia.so?
lingxia-mcp: server w/ sse. sse can run javascript to control lxapp

lingxia builer:
  better name -> publish
  support bun etc. currently, hardcode 'npm'

OH_NativeBundle_ApplicationInfo

navibar: merge hidden into custome style.
on_miniapp_opened -> on_lxapp_opened, miniapps -> lxapps
on_miniapp_closed, and related java swift arkts
move should_override_url_loading to anroide low side/crate, it only anroid needed,

?????????: harmony native 发起创建推送 tsfn， 延迟太大(one solution: precreate run in async, and wait creating done by tsfn callback)
create webview:
homelxapp on init evnet, start creating init route webview, arkts after init returen polling whether
it's done, if not settimeout to let engine has chance to poll tsfn queue to perform creating
on miniapp opened : stop creating init webview, create first for open lxapp at rust
miniapp introduce dispatch(signel thread tokio or pure thread)
引入 dispatch 后， kotlin 等需要移除 Thread/task，因为在 rust 端统一处理了
// 延迟 ms 毫秒后兑现
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}
// 使用示例
async function demo() {
  console.log('start');
  await delay(1000);          // 非阻塞等待 1 秒
  console.log('1s later');
}



about user agent
https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/web-default-useragent



devtools with MCP
1. capture log. realtime  with filter susbsytem/category
2. tools for view: click etc. opendia
3. capture, switch Page(tab)
4. hotreload -- upgrade miniapp packages and restart
5. how to capture request/response for logic fetch

integrate to cloud:
1. download and update
2. login(user, device info)
3. remote log trace(appid, path). collect and zip, upload to cloud
4. cloud function like wechat. lx.cloud.callFunc("name", {})

mobile api
finger print(unique device id)
    figure/device id:
      bind user. after done, next time, only input mobile phone to verify it's in org, device id same, auto login
      bind store with public IP & location
location(integrated)
camera, photo album, qr(harmony: arkts vs native)
file reader: pdf, excel, word, png, native API
deeplink(open lingxia app in dingtak etc, integrated)
push message(huawei, ios, xiaomi, oppo)
audio(lower priority)
huawei login (free, get mobile phone number ?)

platform:
support private crate, add allow attch JS API to JS worker. ！！！！！！！！
builder
App event: on_backgorupd, on_capture

LX JS API:
navigator, tabbar, location ...
体验版,开发版本 基础版本号
reopen miniapp: close(destroy?) and start
OnScrollChangeListener(lower)

Rong:
  Harmony JVM(code done, but not test)
  SSE
  ReadableStream
  V8

extra(lower priority):
printer support
scanner



tauri  icon:
https://github.com/tauri-apps/tauri/blob/cf0b3588a312d9c25ea49e82d40675ea94fcbfdd/crates/tauri-codegen/src/context.rs#L465
https://github.com/tauri-apps/tauri/blob/cf0b3588a312d9c25ea49e82d40675ea94fcbfdd/crates/tauri-utils/src/config.rs#L1225

lx:// Get
https: Get or others. -> is allowed domain ->check lx:// https://  yes, continue to go
                      -> is should proxed -> yes. build Request -> let miniapp to proxy

                          not allowed: reject directly
                          allow: proxyed, or direct to response

                          continue to process
                          take over then proces

                          ios: does not support intercept resoruce request in https ?
                          anroid: only supprot get method ?

 lingxia-webview/ios/lingxia： ios 的实现，其是完备的， 可工作的。 /Users/<USER>/github/LingXia/macosx： 实验性实现， 没有对真正的 naitveAPI， 但 UI 部分是完备的。lingxia-webview/apple 这个是尝试整合到一块的， 需要你帮助 。1. 简化文件名，比如移除 shared，文件名简单明了 2. 确保ios 和 mac延续UI 实现效果 3. 对接 native API， 和 native 交互过程请以 ios 为参考 4. 整体 review 同样实现 apple 下的每一个文件，没一行代码， 以实现高效简洁，文件布局合理，函数分工合理。
